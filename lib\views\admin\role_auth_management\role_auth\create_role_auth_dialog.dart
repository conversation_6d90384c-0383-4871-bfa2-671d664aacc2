import 'package:flutter/material.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/auth_table.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/dialog_type_enum.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';

/// 角色弹窗组件
class CreateRoleAuthDialog extends StatefulWidget {
  final Widget? child;
  final void Function()? onSuccess; // 提交成功回调

  const CreateRoleAuthDialog({super.key, this.child, this.onSuccess});

  @override
  State<CreateRoleAuthDialog> createState() => CreateRoleAuthDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class CreateRoleAuthDialogState extends State<CreateRoleAuthDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  Role currentRole = Role();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final SelectController<String> _groupSelectController = SelectController<String>();
  final SelectController<String> _roleSelectController = SelectController<String>(multiple: true);
  final SelectController<int> _dataScopeenumSelectController = SelectController<int>();

  List<GroupConfig> groups = [];
  List<Role> roles = [];

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  final GlobalKey<AuthTableState> _authTableStateKey = GlobalKey<AuthTableState>();

  /// 加载中
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    currentRole = Role();
    _nameController.text = '';
    _groupSelectController.clear();
    _roleSelectController.clear();
    _dataScopeenumSelectController.clear();
    checkedDepartments = [];
    // if (setDialogState != null) {
    //   setDialogState(() {
    //     // isAddNext = false;
    //   });
    // }
  }

  // /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(String id, StateSetter setDialogState) async {
    try {
      setDialogState(() {
        isLoading = true;
      });

      // 调用详情接口获取完整数据
      final response = await RoleAuthManagementApi.getRoleDetail({'Id': id});

      if (response != null) {
        // 将响应数据转换为 currentRole
        currentRole = Role.fromJson(response);

        _nameController.text = currentRole.name ?? '';
        _groupSelectController.setValue(currentRole.groupId);
        _roleSelectController.setMultiValues(currentRole.parentIdList);
        _dataScopeenumSelectController.setValue(currentRole.dataScopeenum);

        // 部门
        checkedDepartments =
            currentRole.departmentList!
                .map((e) => DepartmentModel(id: e.id, departmentName: e.departmentName))
                .toList();

        _authTableStateKey.currentState?.setCheckedAuthItems(currentRole.powereList);

        isLoading = false;
        setDialogState(() {});
      }
      // 填充表单数据
    } catch (e) {
      isLoading = false;
      setDialogState(() {});
      ToastManager.error('获取详情失败');
    }
  }

  /// 提交数据 - 支持创建和编辑
  Future<void> submitRequest(BuildContext context, StateSetter setDialogState) async {
    // 使用 Form 的校验功能
    if (!_formKey.currentState!.validate()) {
      ToastManager.error('请填写完整信息');
      return;
    }

    setDialogState(() {
      btnLoading = true;
    });

    try {
      const apiMap = {
        // DialogTypeEmun.create: RoleAuthManagementApi.addOrEditRole,
        DialogTypeEmun.edit: RoleAuthManagementApi.addOrEditRole,
      };

      // if (_dialogType == DialogTypeEmun.create) {
      //   currentRole.id = '00000000-0000-0000-0000-000000000000';
      // }

      //选中的部门
      if (currentRole.dataScopeenum == 5) {
        currentRole.departmentIdList = checkedDepartments.map((e) => e.id!).toList();
      } else {
        currentRole.departmentIdList = [];
      }

      /// 选中的权限
      var authList = _authTableStateKey.currentState?.checkedAuthIds ?? [];
      currentRole.powereList = authList;

      await apiMap[_dialogType]!(currentRole.toJson());

      ToastManager.success('操作成功');

      setDialogState(() {
        btnLoading = false;
      });

      // 只有创建模式才重置表单（编辑模式直接关闭）
      if (_dialogType == DialogTypeEmun.create) {
        resetFormData(setDialogState);
      }

      widget.onSuccess?.call();

      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } catch (err) {
      setDialogState(() {
        btnLoading = true;
      });
    }
  }

  /// 打开角色弹窗 - 支持创建和编辑
  Future<void> showCreateRoleAuthDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
    // Role? role,
  }) async {
    _dialogType = type;

    /// 详情是否已经加载（确保只能调用一次接口）
    bool isDetailLoaded = false;

    final response = await RoleAuthManagementApi.getList({});

    groups =
        (response as List).map((e) => GroupConfig.fromJson(e as Map<String, dynamic>)).toList();
    roles = groups.expand((g) => g.roleList ?? []).cast<Role>().toList();

    // 重置表单数据
    resetFormData();

    // // 如果是编辑模式，先加载数据再显示弹窗
    // if (type == DialogTypeEmun.edit && id != null) {
    //   // currentRole.id = id;
    //   // currentRole = role;
    //   fillEditData(id);
    // }

    // 检查组件是否仍然挂载
    if (!mounted) return;

    double labelWidth = 100;

    // 名称
    Widget buildNameInput() {
      return AppInput(
        label: "名称",
        required: true,
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "名称",
        disabled: true,
        size: InputSize.medium,
        controller: _nameController,
        maxLength: 30,
        validator: (value) {
          // 优先使用 controller 的值进行验证，确保编辑模式下能正确获取值
          final currentValue = (value != null && value.isNotEmpty) ? value : _nameController.text;

          if (currentValue.isEmpty) {
            return '请输入名称';
          }
          return null;
        },
        onChanged: (value) {
          currentRole.name = value;
        },
      );
    }

    // 所属分组
    Widget buildGroupSelect() {
      return AppFormField(
        label: '所属分组',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_groupSelectController.value == null) {
            return '请选择分组';
          }
          return null;
        },
        builder:
            (field) => AppSelect<String>(
              placeholder: '性别',
              disabled: true,
              options:
                  groups.isNotEmpty
                      ? groups
                          .map((g) => SelectOption<String>(value: g.id!, label: g.name))
                          .toList()
                      : [],
              controller: _groupSelectController,
              onChanged: (value) {
                field.didChange(value);
                currentRole.groupId = value!;
              },
            ),
      );
    }

    // 父角色
    Widget buildRoleSelect() {
      List<SelectOption<String>> roleOptions = [];

      if (roles.isNotEmpty) {
        roleOptions = roles.map((g) => SelectOption<String>(value: g.id!, label: g.name)).toList();
        if (_dialogType == DialogTypeEmun.edit) {
          roleOptions = roleOptions.where((r) => r.value != currentRole.id).toList();
        }
      }

      return AppFormField(
        label: '父角色',
        required: false,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        // validator: (value) {
        //   if (_roleSelectController.multiValues.isEmpty) {
        //     return '请选择父角色';
        //   }
        //   return null;
        // },
        builder:
            (field) => AppSelect<String>(
              placeholder: '请选择',
              disabled: true,
              multiple: true,
              options: roleOptions,
              controller: _roleSelectController,
              // onChanged: (value) {
              //   field.didChange(value);
              //   currentRole.groupId = value!;
              // },
              onMultiChanged: (values) {
                currentRole.parentIdList = values;
              },
            ),
      );
    }

    /// 数据管理范围（待处理）
    Widget buildDataScopeenumSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '数据管理范围',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_dataScopeenumSelectController.value == null ||
              _dataScopeenumSelectController.value == 0) {
            return '请选择数据管理范围';
          }
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '请选择',
              options: RoleAuthVar.dataScopeenums,
              controller: _dataScopeenumSelectController,
              onChanged: (value) {
                field.didChange(value.toString());
                setDialogState(() {
                  currentRole.dataScopeenum = value!;
                });
              },
            ),
      );
    }

    // 部门选择
    Widget buildDepartmentSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '部门',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (currentRole.dataScopeenum == 5 && checkedDepartments.isEmpty) {
            return '请选择部门';
          }
          return null;
        },
        builder:
            (field) => DepartmentSelector(
              checkStrictly: true,
              defaultCheckedDepartment: checkedDepartments,
              onChange: (value) {
                field.didChange(value);
                setDialogState(() {
                  checkedDepartments = value;
                });
              },
            ),
      );
    }

    // 分割线
    Widget buildSplitTitle() {
      return Stack(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(IconFont.mianxing_xiala, color: context.icon100, size: 16),
              Padding(padding: EdgeInsets.symmetric(horizontal: 4), child: Text('权限范围')),
              Expanded(child: Divider(color: context.border200)),
            ],
          ),
        ],
      );
    }

    AppDialog.show(
      width: 1000,
      // height: 600,
      context: context,
      title: '编辑企业角色权限',
      // isDrawer: true,
      // slideDirection: SlideDirection.right,
      showFooter: false,
      barrierDismissible: true,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          // 如果是编辑模式，先加载数据再显示弹窗
          if (!isDetailLoaded && type == DialogTypeEmun.edit && id != null) {
            // currentRole.id = id;
            // currentRole = role;
            isDetailLoaded = true;
            fillEditData(id, setDialogState);
          }

          return AppLoading(
            isLoading: isLoading,
            child: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 10,
                        children: [
                          buildNameInput(),
                          buildGroupSelect(),
                          buildRoleSelect(),
                          buildDataScopeenumSelect(setDialogState),

                          if (currentRole.dataScopeenum == 5) buildDepartmentSelect(setDialogState),

                          buildSplitTitle(),

                          Expanded(child: AuthTable(key: _authTableStateKey)),
                        ],
                      ),
                    ),
                  ),
                ),
                Divider(),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // 只有创建模式才显示"继续新建下一条"选项
                      // if (_dialogType == DialogTypeEmun.create) ...[
                      //   Checkbox(
                      //     value: isAddNext,
                      //     onChanged: (value) {
                      //       setDialogState(() {
                      //         isAddNext = !isAddNext;
                      //       });
                      //     },
                      //   ),
                      //   Text('继续新建下一条'),
                      //   const SizedBox(width: 10),
                      // ],
                      AppButton(
                        text: '取消',
                        type: ButtonType.default_,
                        onPressed: () => context.pop(),
                      ),
                      const SizedBox(width: 10),
                      AppButton(
                        text: '确定',
                        type: ButtonType.primary,
                        loading: btnLoading,
                        onPressed: () => submitRequest(context, setDialogState),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _groupSelectController.dispose();
    _roleSelectController.dispose();
    _dataScopeenumSelectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
