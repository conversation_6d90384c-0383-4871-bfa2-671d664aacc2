import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';

part 'role.g.dart';

@JsonSerializable()
class Role {
  @JsonKey(name: 'Id')
  String? id;

  @JsonKey(name: 'Name', defaultValue: '')
  String name;

  @Json<PERSON>ey(name: 'GroupId', defaultValue: '')
  String groupId;

  @JsonKey(name: 'GroupName', defaultValue: '')
  String groupName;

  @JsonKey(name: 'ParentIdList', defaultValue: [])
  List<String> parentIdList;

  /// 系统配置 1.是 2.不是
  @JsonKey(name: 'Defaultenum')
  final int defaultenum;

  /// 数据管理范围（1：全组织；2：本部门及以下部门；3：本部门；4：本人数据；5：指定部门）
  @JsonKey(name: 'DataScopeenum', defaultValue: 1)
  int dataScopeenum;

  @Json<PERSON>ey(name: 'PowereList', defaultValue: [])
  List<int> powereList;

  @JsonKey(name: 'DepartmentIdList', defaultValue: [])
  List<String> departmentIdList;

  @JsonKey(name: 'DepartmentList', defaultValue: <DepartmentModel>[])
  List<DepartmentModel>? departmentList;

  /// 是否显示（用于UI）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isVisible;

  Role({
    this.id,
    this.name = '',
    this.groupId = '',
    this.groupName = '',
    this.parentIdList = const [],
    this.powereList = const [],
    this.departmentIdList = const [],
    this.departmentList = const [],
    this.dataScopeenum = 1,
    this.defaultenum = 2,
    this.isVisible = false,
  });

  /// 数据管理范围枚举值到描述的映射
  static String getDataScopeDescription(int dataScopeEnum) {
    var obj = RoleAuthVar.dataScopeenums.firstWhereOrNull((c) => c.value == dataScopeEnum);
    if (obj == null) {
      return '未知类型';
    }
    return obj.label;
  }

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);

  Map<String, dynamic> toJson() => _$RoleToJson(this);
}
