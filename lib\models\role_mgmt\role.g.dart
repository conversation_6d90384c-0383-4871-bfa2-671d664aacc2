// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
  id: json['Id'] as String?,
  name: json['Name'] as String? ?? '',
  groupId: json['GroupId'] as String? ?? '',
  groupName: json['GroupName'] as String? ?? '',
  parentIdList:
      (json['ParentIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  powereList:
      (json['PowereList'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList() ??
      [],
  departmentIdList:
      (json['DepartmentIdList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  departmentList:
      (json['DepartmentList'] as List<dynamic>?)
          ?.map((e) => DepartmentModel.fromJson(e as Map<String, dynamic>))
          .toList() ??
      [],
  dataScopeenum: (json['DataScopeenum'] as num?)?.toInt() ?? 1,
  defaultenum: (json['Defaultenum'] as num?)?.toInt() ?? 2,
);

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
  'Id': instance.id,
  'Name': instance.name,
  'GroupId': instance.groupId,
  'GroupName': instance.groupName,
  'ParentIdList': instance.parentIdList,
  'Defaultenum': instance.defaultenum,
  'DataScopeenum': instance.dataScopeenum,
  'PowereList': instance.powereList,
  'DepartmentIdList': instance.departmentIdList,
  'DepartmentList': instance.departmentList,
};
