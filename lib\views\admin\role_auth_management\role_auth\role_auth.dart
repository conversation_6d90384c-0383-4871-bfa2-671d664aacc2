import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/batch_assign_dialog.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/create_role_auth_dialog.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/default_tag.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/dialog_type_enum.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_auth_var.dart';
import 'package:octasync_client/views/admin/role_auth_management/role_auth/role_type_tag.dart';
import 'package:overflow_view/overflow_view.dart';

class RoleAuth extends StatefulWidget {
  const RoleAuth({super.key});

  @override
  State<RoleAuth> createState() => _RoleAuthState();
}

class _RoleAuthState extends State<RoleAuth> {
  double titleHeight = 48;
  late AppTableStateManage _stateManage;

  final GlobalKey<BatchAssignDialogState> _batchAssignDialogStateKey =
      GlobalKey<BatchAssignDialogState>();

  final GlobalKey<CreateRoleAuthDialogState> _createRoleAuthDialogStateKey =
      GlobalKey<CreateRoleAuthDialogState>();

  late var colsTemp = [
    AppTableColumn(
      text: '角色名称',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 200,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '类型',
      field: 'Defaultenum',
      type: AppTableColumnType.text(),
      width: 100,
      resizable: true, // 允许调整列宽
      cellBuilder: (context, value, column, row) {
        return RoleTypeTag(roleType: value);
      },
    ),
    AppTableColumn(
      text: '分组',
      field: 'GroupName',
      type: AppTableColumnType.text(),
      width: 140,
      resizable: true, // 允许调整列宽
      cellBuilder: (context, value, column, row) {
        return DefaultTag(text: value);
      },
    ),
    AppTableColumn(
      text: '数据管理范围',
      field: 'DataScopeenum',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 140,
      resizable: true, // 允许调整列宽
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        // 测试
        // String activeValueTemp = '行3列3';
        // return Container(
        //   padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //   decoration: BoxDecoration(
        //     color: value == activeValueTemp ? Colors.green : Colors.red,
        //     borderRadius: BorderRadius.circular(4),
        //   ),
        //   child: Text(
        //     value == activeValueTemp ? '活跃' : '禁用',
        //     style: TextStyle(color: Colors.white),
        //   ),
        // );

        // return Text('${value}');

        return DefaultTag(text: Role.getDataScopeDescription(value as int));
      },
    ),

    AppTableColumn(
      text: '权限范围',
      field: 'PowereList',
      type: AppTableColumnType.text(),
      width: 300,
      resizable: true, // 允许调整列宽
      cellBuilder: (context, value, column, row) {
        List<Widget> widgets = [];
        if (value is List && value.isNotEmpty) {
          var roles = value.cast<int>();

          for (var i = 0; i < roles.length; i++) {
            var allRoles = RoleAuthVar.generateGroups().expand((g) => g.items);
            var role = allRoles.firstWhereOrNull((r) => r.authId == roles[i]);
            if (role != null) {
              widgets.add(DefaultTag(text: role.label));
            }
          }
        }
        return Stack(
          children: [
            Container(
              width: column.width - 30,
              child: OverflowView.flexible(
                spacing: 4,
                children: widgets,
                builder: (context, remaining) {
                  return DefaultTag(text: '+$remaining');
                },
              ),
            ),
            // // 展示全部
            // Positioned(
            //   child: Container(
            //     padding: EdgeInsets.symmetric(vertical: 15),
            //     color: Colors.red,
            //     width: column.width - 10,
            //     child: Wrap(children: widgets),
            //   ),
            // ),
          ],
        );
      },
    ),

    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      // alignment: Alignment.center,
      width: 80,
      resizable: true, // 允许调整列宽
      showMore: false,
      // headerBuilder: (context) {
      //   return Row(
      //     mainAxisAlignment: MainAxisAlignment.center,
      //     children: [
      //       Text('自定义列名称'),
      //       SizedBox(width: 4),
      //       Icon(Icons.star_border, size: 16, color: Colors.red),
      //     ],
      //   );
      // },
      cellBuilder: (context, value, column, row) {
        return AppDropdown(
          items: [
            DropdownItem(text: '编辑', value: 'edit'),
            // DropdownItem(text: '删除', value: 'delete'),
          ],
          trigger: DropdownTrigger.click,
          onItemSelected: (item) {
            switch (item.value) {
              case 'edit':
                // 调用编辑弹窗，传入ID用于获取详情
                final id = row['Id']?.toString();
                if (id != null) {
                  _createRoleAuthDialogStateKey.currentState?.showCreateRoleAuthDialog(
                    context,
                    type: DialogTypeEmun.edit,
                    id: id,
                  );
                }

                break;
              case 'delete':
                // 从行数据中获取ID并传递给deleteItem方法
                // final id = row['Id']?.toString();
                // deleteItem(id!);
                break;
              default:
            }
          },
          child: Icon(IconFont.xianxing_gengduo),
        );
      },
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              _TitleBlock(
                key: ValueKey('title_2'),
                title: '权限配置',
                lineColor: context.textSecondary,
                titleHeight: titleHeight,
              ),

              Padding(
                padding: EdgeInsets.only(top: 10, right: 10, left: 10),
                child: Row(
                  children: [
                    AppDropdown(
                      text: '批量操作',
                      items: [DropdownItem(text: '批量指定数据范围', value: 'assign')],
                      type: DropdownType.primary,
                      onItemSelected: (item) {
                        if (item.value == 'assign') {
                          // 调用编辑弹窗，传入ID用于获取详情
                          // final id = row['Id']?.toString();
                          // if (id != null) {}

                          if (_stateManage.checkedRows.isEmpty) {
                            ToastManager.error('请选择需要指定的角色');
                            return;
                          }

                          print('选中的行：${_stateManage.checkedRows.map((r) => r.id)}');

                          _batchAssignDialogStateKey.currentState?.showBatchAssignDialog(
                            context,
                            roleIdList: _stateManage.checkedRows.map((r) => r.id).toList(),
                          );
                        }

                        // setState(() {
                        //   selectedValues['typePrimary'] = item.value;
                        // });
                      },
                      // value: selectedValues['typePrimary'],
                    ),
                  ],
                ),
              ),

              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(10),
                  child: AppTable(
                    // key: ValueKey(currentTabId),
                    // loading: isLoading,
                    // columns: columns,
                    // rows: rows,
                    // columns: [],
                    // rows: [],
                    checkType: TableCheckedEnum.multiple,
                    indexColumnWidth: 100,
                    defaultExpandAll: true,
                    maxLevel: 6,

                    uniqueId: 'Id',

                    /// 树形结构配置（扁平结构不需要传递树形结构配置参数）
                    // treeConfig: TreeTableConfig(
                    //   idField: 'nodeId',
                    //   parentIdField: 'pid',
                    //   emptyParentValue: null,
                    // ),
                    showAddRowButton: false,
                    showAddColumnButton: false,
                    // listviewPddingBottom: 0,

                    /// 索引列自定义
                    // indexCellBuilder: (context, index) {
                    //   return Text('第${index}条');
                    // },
                    onCellTap: (context, rowIndex, columnIndex, value) {
                      String msg = '点击了第${rowIndex}行，第${columnIndex}列，值为：$value';
                      print(msg);
                    },
                    onLoaded: (stateManage) {
                      _stateManage = stateManage;

                      _stateManage.setColumns(colsTemp);

                      getList();
                    },
                    onLoadMore: () {
                      // loadMore();
                    },

                    // onAddColumn: (
                    //   context,
                    //   AppTableColumn column,
                    //   AppTableStateManage stateManager,
                    //   double tableViewportWidth,
                    // ) {
                    //   print('添加列：${column}');

                    //   if (columns.length < 10) {
                    //     setState(() {
                    //       //添加新列
                    //       columns.add(column);

                    //       //新增列的唯一标识符
                    //       var newField = column.field;

                    //       print(newField);

                    //       // //所有的行补充新增列属性
                    //       // for (var row in rows) {
                    //       //   row.data[newField] = '';
                    //       // }
                    //     });

                    //     // WidgetsBinding.instance.addPostFrameCallback((_) {
                    //     //   //直接使用传入的状态管理实例执行滚动调整逻辑
                    //     //   stateManager.adjustScrollPositionAfterAddColumn(
                    //     //     tableViewportWidth,
                    //     //     column.width,
                    //     //   );
                    //     // });
                    //   } else {
                    //     print('表格列不能超过10列-------------------');
                    //   }

                    //   // print('添加了新列：${column.field} ${column.text} ${column.width}');

                    //   // AppTableGeneralHelper.newColumn(1000);
                    // },
                  ),
                ),
              ),
            ],
          ),

          /// 批量指定数据范围
          BatchAssignDialog(
            key: _batchAssignDialogStateKey,
            onSuccess: () {
              getList();
            },
          ),

          /// 编辑角色
          CreateRoleAuthDialog(
            key: _createRoleAuthDialogStateKey,
            onSuccess: () {
              getList();
            },
          ),
        ],
      ),
    );
  }

  Future<void> getList() async {
    _stateManage.setLoading(true);

    try {
      var postDatas = {'pageSize': 10000, 'pageIndex': 1};
      final response = await RoleAuthManagementApi.getRoleList(postDatas);
      _stateManage.setLoading(false);

      // print(jsonEncode(response));

      // PagesModel<RoleAuthModel> _pages = PagesModel.fromJson(
      //   response,
      //   (json) => RoleAuthModel.fromJson(json as Map<String, dynamic>),
      // );
      // List<RoleAuthModel> _list = _pages.items;

      // print('object====${_list.length} ${(response['Items'] as List).length}');
      // //所有分组
      // var groups =
      //     (response['Items'] as List)
      //         .map((e) => RoleAuthModel.fromJson(e as Map<String, dynamic>))
      //         .toList();

      // print('object ${groups}');

      List<Map<String, dynamic>> jsonRowsStr =
          (response['Items'] as List).map((r) => r as Map<String, dynamic>).toList();

      var rows = _stateManage.convertToTreeData(jsonRowsStr);
      _stateManage.setRows(rows);
    } catch (e) {
      _stateManage.setLoading(false);
    }
  }
}

class _TitleBlock extends StatelessWidget {
  final String title;
  final double titleHeight;
  final Color lineColor;

  const _TitleBlock({
    super.key,
    required this.title,
    this.titleHeight = 48,
    this.lineColor = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: titleHeight,
      width: 200, //double.infinity,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: context.border300, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start, // 左对齐
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: context.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
